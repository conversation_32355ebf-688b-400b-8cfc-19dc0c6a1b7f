"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { createSupabaseBrowserClient, createUserProfile } from '@/lib/supabase'
import { Badge } from '@/components/ui/badge'

interface AuthUser {
  id: string
  email: string
  created_at: string
}

interface ProfileUser {
  id: string
  email: string
  full_name: string | null
  role: string
  created_at: string
}

export default function UserManagementPage() {
  const [authUsers, setAuthUsers] = useState<AuthUser[]>([])
  const [profileUsers, setProfileUsers] = useState<ProfileUser[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const supabase = createSupabaseBrowserClient()

  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError('')

      // Get users from auth.users (this requires service role, so it might fail)
      console.log('Fetching auth users...')
      
      // Get users from public.users table
      const { data: profiles, error: profileError } = await supabase
        .from('users')
        .select('id, email, full_name, role, created_at')
        .order('created_at', { ascending: false })

      if (profileError) {
        console.error('Error fetching profiles:', profileError)
        setError(`Error fetching profiles: ${profileError.message}`)
      } else {
        setProfileUsers(profiles || [])
        console.log('Profiles found:', profiles?.length || 0)
      }

    } catch (error) {
      console.error('Error fetching users:', error)
      setError(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const createMissingProfile = async (userId: string, email: string) => {
    try {
      setError('')
      setSuccess('')
      
      console.log(`Creating profile for user ${email}...`)
      
      // Create a mock user object for profile creation
      const mockUser = {
        id: userId,
        email: email,
        user_metadata: {
          full_name: email.split('@')[0], // Use email prefix as name
          name: email.split('@')[0]
        }
      }

      const { data, error } = await createUserProfile(mockUser, 'student')
      
      if (error) {
        setError(`Failed to create profile for ${email}: ${error.message}`)
      } else {
        setSuccess(`Profile created successfully for ${email}`)
        fetchUsers() // Refresh the list
      }
    } catch (error) {
      console.error('Error creating profile:', error)
      setError(`Error creating profile: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const deleteProfile = async (userId: string, email: string) => {
    try {
      setError('')
      setSuccess('')
      
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', userId)

      if (error) {
        setError(`Failed to delete profile for ${email}: ${error.message}`)
      } else {
        setSuccess(`Profile deleted for ${email}`)
        fetchUsers() // Refresh the list
      }
    } catch (error) {
      console.error('Error deleting profile:', error)
      setError(`Error deleting profile: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground">Manage Supabase Auth users and their profiles</p>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <AlertDescription className="text-green-600">{success}</AlertDescription>
          </Alert>
        )}

        <div className="flex gap-4">
          <Button onClick={fetchUsers} disabled={loading}>
            {loading ? 'Loading...' : 'Refresh Users'}
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Profile Users */}
          <Card>
            <CardHeader>
              <CardTitle>Users with Profiles ({profileUsers.length})</CardTitle>
              <CardDescription>
                Users who have records in the public.users table
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {profileUsers.length === 0 ? (
                  <p className="text-muted-foreground text-center py-4">
                    No users with profiles found
                  </p>
                ) : (
                  profileUsers.map((user) => (
                    <div key={user.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="space-y-1">
                        <p className="font-medium">{user.email}</p>
                        <p className="text-sm text-muted-foreground">
                          {user.full_name || 'No name'}
                        </p>
                        <div className="flex gap-2">
                          <Badge variant="secondary">{user.role}</Badge>
                          <Badge variant="outline">
                            {new Date(user.created_at).toLocaleDateString()}
                          </Badge>
                        </div>
                      </div>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => deleteProfile(user.id, user.email)}
                      >
                        Delete Profile
                      </Button>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Instructions */}
          <Card>
            <CardHeader>
              <CardTitle>Instructions</CardTitle>
              <CardDescription>
                How to fix authentication issues
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Current Situation:</h4>
                <ul className="text-sm text-muted-foreground space-y-1 list-disc list-inside">
                  <li>You have {profileUsers.length} users with profiles</li>
                  <li>Users exist in Supabase Auth but may be missing profiles</li>
                  <li>This causes login issues</li>
                </ul>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">To Fix:</h4>
                <ol className="text-sm text-muted-foreground space-y-1 list-decimal list-inside">
                  <li>Try logging in with your existing account</li>
                  <li>Check the browser console for detailed error messages</li>
                  <li>The auth context will now auto-create missing profiles</li>
                  <li>If it fails, use the force logout page to clear everything</li>
                </ol>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Useful Links:</h4>
                <div className="space-y-1">
                  <Button variant="outline" size="sm" asChild>
                    <a href="/force-logout">Force Logout</a>
                  </Button>
                  <Button variant="outline" size="sm" asChild>
                    <a href="/auth/login">Login Page</a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
