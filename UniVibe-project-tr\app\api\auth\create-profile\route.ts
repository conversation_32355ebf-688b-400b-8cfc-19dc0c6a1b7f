import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

// Create a Supabase client with service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, email, fullName, university, role = 'student' } = body

    console.log('🔍 API: Creating profile for user:', { userId, email, fullName, university, role })

    // Validate required fields
    if (!userId || !email) {
      return NextResponse.json(
        { error: 'Missing required fields: userId and email' },
        { status: 400 }
      )
    }

    // Check if profile already exists
    const { data: existingProfile, error: checkError } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', userId)
      .single()

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('🚨 API: Error checking existing profile:', checkError)
      return NextResponse.json(
        { error: 'Database error checking existing profile' },
        { status: 500 }
      )
    }

    if (existingProfile) {
      console.log('✅ API: Profile already exists:', existingProfile)
      return NextResponse.json({ data: existingProfile })
    }

    // Create the profile data
    const profileData = {
      id: userId,
      email: email,
      full_name: fullName || email.split('@')[0] || 'User',
      avatar_url: null,
      role: role,
      university: university || null,
    }

    console.log('🔍 API: Inserting profile data:', profileData)

    // Insert the profile using admin client (bypasses RLS)
    const { data, error } = await supabaseAdmin
      .from('users')
      .insert(profileData)
      .select()
      .single()

    if (error) {
      console.error('🚨 API: Profile creation error:', error)
      return NextResponse.json(
        { 
          error: 'Failed to create profile',
          details: {
            code: error.code,
            message: error.message,
            details: error.details,
            hint: error.hint
          }
        },
        { status: 500 }
      )
    }

    console.log('✅ API: Profile created successfully:', data)
    return NextResponse.json({ data })

  } catch (error) {
    console.error('🚨 API: Unexpected error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
