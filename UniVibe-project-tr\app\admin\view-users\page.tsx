"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { createSupabaseBrowserClient } from '@/lib/supabase'
import { RefreshCw, Users, Database } from 'lucide-react'

interface User {
  id: string
  email: string
  full_name: string | null
  role: string
  university: string | null
  avatar_url: string | null
  created_at: string
  updated_at: string | null
}

export default function ViewUsersPage() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)
  const supabase = createSupabaseBrowserClient()

  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError('')
      console.log('Fetching users from database...')

      const { data, error: fetchError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false })

      if (fetchError) {
        console.error('Error fetching users:', fetchError)
        setError(`Error fetching users: ${fetchError.message}`)
      } else {
        console.log('Users fetched successfully:', data?.length || 0, 'users')
        setUsers(data || [])
        setLastRefresh(new Date())
      }
    } catch (error) {
      console.error('Unexpected error fetching users:', error)
      setError(`Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchUsers()
  }, [])

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getRoleColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'student':
        return 'bg-blue-100 text-blue-800'
      case 'organizer':
        return 'bg-green-100 text-green-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-3">
            <div className="p-3 rounded-2xl bg-gradient-to-br from-primary to-accent">
              <Database className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold">Users Database</h1>
          </div>
          <p className="text-muted-foreground">
            View all user accounts stored in the Supabase users table
          </p>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button onClick={fetchUsers} disabled={loading} className="flex items-center gap-2">
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
            {lastRefresh && (
              <p className="text-sm text-muted-foreground">
                Last updated: {lastRefresh.toLocaleTimeString()}
              </p>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-5 w-5 text-muted-foreground" />
            <span className="text-lg font-semibold">{users.length} users</span>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Users Display */}
        {loading ? (
          <Card>
            <CardContent className="flex items-center justify-center py-12">
              <div className="text-center space-y-4">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
                <p className="text-muted-foreground">Loading users...</p>
              </div>
            </CardContent>
          </Card>
        ) : users.length === 0 ? (
          <Card>
            <CardContent className="text-center py-12">
              <div className="space-y-4">
                <div className="text-6xl">👤</div>
                <h3 className="text-xl font-semibold">No users found</h3>
                <p className="text-muted-foreground">
                  The users table is empty. Create a new account to see it appear here.
                </p>
                <div className="flex gap-2 justify-center">
                  <Button asChild>
                    <a href="/auth/signup">Create Account</a>
                  </Button>
                  <Button variant="outline" asChild>
                    <a href="/admin/clear-auth">Clear All Data</a>
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-4">
            {users.map((user) => (
              <Card key={user.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <CardTitle className="text-lg">
                        {user.full_name || 'No name provided'}
                      </CardTitle>
                      <CardDescription className="font-mono text-sm">
                        {user.email}
                      </CardDescription>
                    </div>
                    <Badge className={getRoleColor(user.role)}>
                      {user.role}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-muted-foreground">University</p>
                      <p className="font-medium">{user.university || 'Not specified'}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">User ID</p>
                      <p className="font-mono text-xs">{user.id}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Created</p>
                      <p className="font-medium">{formatDate(user.created_at)}</p>
                    </div>
                    <div>
                      <p className="text-muted-foreground">Last Updated</p>
                      <p className="font-medium">
                        {user.updated_at ? formatDate(user.updated_at) : 'Never'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Useful links for testing and managing authentication
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <Button variant="outline" asChild>
                <a href="/auth/signup">Create Account</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/auth/login">Login</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/admin/clear-auth">Clear All Data</a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/force-logout">Force Logout</a>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
