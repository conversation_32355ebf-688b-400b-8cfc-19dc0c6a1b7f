"use client"

import { FilterPills } from "@/components/filter-pills"
import { EventCard } from "@/components/event-card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useState, useEffect } from "react"
import { Sparkles, Search, X, Plus } from "lucide-react"
import { getEvents } from "@/lib/events"
import { useRouter, useSearchParams } from "next/navigation"
import { useAuth } from "@/lib/auth-context"

// Mock data
const categories = ["All", "Music", "Clubs", "Academic", "Sports", "Food", "Arts", "Other"]

const mockEvents = [
  {
    id: "1",
    title: "Spring Music Festival 2024",
    date: "March 15",
    time: "7:00 PM",
    location: "University Quad",
    organizer: "Music Society",
    attendees: 234,
    category: "Music",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "2",
    title: "Tech Talk: AI in Healthcare",
    date: "March 18",
    time: "2:00 PM",
    location: "Engineering Building, Room 101",
    organizer: "Computer Science Club",
    attendees: 89,
    category: "Academic",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "3",
    title: "Basketball Championship Finals",
    date: "March 20",
    time: "6:00 PM",
    location: "Sports Complex",
    organizer: "Athletics Department",
    attendees: 456,
    category: "Sports",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "4",
    title: "Art Gallery Opening Night",
    date: "March 22",
    time: "5:30 PM",
    location: "Student Center Gallery",
    organizer: "Art Club",
    attendees: 67,
    category: "Arts",
    image: "/placeholder.svg?height=192&width=400",
  },
  {
    id: "5",
    title: "Food Truck Festival",
    date: "March 25",
    time: "11:00 AM",
    location: "Campus Green",
    organizer: "Student Government",
    attendees: 312,
    category: "Food",
    image: "/placeholder.svg?height=192&width=400",
  },
]

export default function HomePage() {
  const [activeCategory, setActiveCategory] = useState("All")
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const searchParams = useSearchParams()
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login')
    }
  }, [user, authLoading, router])

  // Handle URL search params
  useEffect(() => {
    const searchParam = searchParams.get('search')
    if (searchParam) {
      setSearchQuery(decodeURIComponent(searchParam))
      // Reset category to "All" when searching to avoid filtering conflicts
      setActiveCategory("All")
    } else {
      setSearchQuery("")
    }
  }, [searchParams])

  // Fetch events from Supabase
  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const { data, error } = await getEvents()
        if (error) {
          console.error('Error fetching events:', error)
          // Fallback to mock data if there's an error
          setEvents(mockEvents)
        } else {
          // Transform Supabase data to match EventCard props
          const transformedEvents = data?.map((event: any) => ({
            id: event.id,
            title: event.title,
            date: formatDate(event.date),
            time: event.time,
            location: event.location,
            organizer: event.organizer,
            attendees: event.attendees_count || 0,
            category: event.category,
            image: event.image_url || "/placeholder.svg?height=192&width=400",
          })) || []
          setEvents(transformedEvents)
        }
      } catch (error) {
        console.error('Unexpected error fetching events:', error)
        setEvents(mockEvents)
      } finally {
        setLoading(false)
      }
    }

    fetchEvents()
  }, [])

  // Helper function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return "TBD"
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric'
    })
  }

  // Filter events by category and search query
  const filteredEvents = events.filter((event) => {
    const matchesCategory = activeCategory === "All" || event.category === activeCategory
    const matchesSearch = !searchQuery ||
      event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
      event.organizer.toLowerCase().includes(searchQuery.toLowerCase())

    return matchesCategory && matchesSearch
  })

  // Show loading while auth is being checked
  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto"></div>
          <h2 className="text-xl font-semibold">Loading UniVibe...</h2>
          <p className="text-muted-foreground">Checking authentication</p>
        </div>
      </div>
    )
  }

  // Don't render content if user is not authenticated
  if (!user) {
    return null
  }

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="text-center space-y-2 py-4">
        <h2 className="text-2xl font-bold gradient-text">Discover Amazing Events</h2>
        <p className="text-muted-foreground">Find and join events happening around your campus</p>
        {searchQuery && (
          <div className="mt-4 p-3 bg-accent/10 border border-accent/20 rounded-lg">
            <p className="text-sm text-accent">
              🔍 Searching for: <span className="font-semibold">"{searchQuery}"</span>
            </p>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <Sparkles className="w-5 h-5 text-accent" />
          Categories
        </h3>
        <FilterPills categories={categories} activeCategory={activeCategory} onCategoryChange={setActiveCategory} />
      </div>

      {/* Events Grid */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            {searchQuery
              ? `Search Results${activeCategory !== "All" ? ` in ${activeCategory}` : ""}`
              : activeCategory === "All" ? "All Events" : `${activeCategory} Events`
            }
          </h3>
          <span className="text-sm text-muted-foreground">
            {filteredEvents.length} event{filteredEvents.length !== 1 ? "s" : ""} found
          </span>
        </div>

        {/* Responsive Grid Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 lg:gap-8">
          {loading ? (
            // Enhanced Loading skeleton with responsive grid
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="animate-pulse">
                <div className="bg-gradient-to-br from-card/50 to-card/30 backdrop-blur-sm rounded-xl h-80 w-full shadow-lg border border-white/10">
                  <div className="p-6 space-y-4">
                    <div className="h-4 bg-muted/50 rounded-lg w-3/4"></div>
                    <div className="h-3 bg-muted/30 rounded w-1/2"></div>
                    <div className="space-y-2">
                      <div className="h-3 bg-muted/30 rounded"></div>
                      <div className="h-3 bg-muted/30 rounded w-5/6"></div>
                    </div>
                    <div className="flex justify-between items-center pt-4">
                      <div className="h-6 bg-muted/40 rounded-full w-16"></div>
                      <div className="h-8 bg-muted/40 rounded-lg w-20"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          ) : filteredEvents.length > 0 ? (
            filteredEvents.map((event, index) => (
              <div
                key={event.id}
                className="animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <EventCard {...event} />
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-20">
              <div className="relative mb-8">
                <div className="p-8 rounded-3xl bg-gradient-to-br from-accent/20 to-primary/20 w-fit mx-auto border border-accent/30 backdrop-blur-sm">
                  <Sparkles className="h-16 w-16 text-accent" />
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center animate-pulse">
                  <Search className="h-4 w-4 text-white" />
                </div>
              </div>
              <h3 className="text-2xl font-bold mb-3 bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                {searchQuery ? "🔍 No matching events found" : "🎉 No events yet"}
              </h3>
              <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                {searchQuery
                  ? `We couldn't find any events matching "${searchQuery}"${activeCategory !== "All" ? ` in the ${activeCategory} category` : ""}. Try adjusting your search terms or browse all categories.`
                  : activeCategory === "All"
                  ? "No events are currently available. Be the first to create an amazing event for your community!"
                  : `No events found in the ${activeCategory} category. Check out other categories or create your own event!`
                }
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                {searchQuery && (
                  <Button
                    onClick={() => {
                      setSearchQuery("")
                      setActiveCategory("All")
                      router.push("/")
                    }}
                    variant="outline"
                    className="border-white/20 hover:bg-accent/10"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Clear Search
                  </Button>
                )}
                <Button
                  onClick={() => router.push("/post")}
                  className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Create Event
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
