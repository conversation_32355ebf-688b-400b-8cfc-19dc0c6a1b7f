"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    try {\n        let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n            request: {\n                headers: request.headers\n            }\n        });\n        // Skip middleware for static files and API routes\n        const { pathname } = request.nextUrl;\n        if (pathname.startsWith('/_next/') || pathname.startsWith('/api/') || pathname.includes('.')) {\n            return response;\n        }\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://wbzlnrekvqjkjvxkohwf.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndiemxucmVrdnFqa2p2eGtvaHdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDE2NDksImV4cCI6MjA2MjkxNzY0OX0.ahMF1_RXeu-D6jJ6EF8TNzl-zJRSdy-yqwB4wWzL98Y\", {\n            cookies: {\n                get (name) {\n                    return request.cookies.get(name)?.value;\n                },\n                set (name, value, options) {\n                    request.cookies.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                    response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request: {\n                            headers: request.headers\n                        }\n                    });\n                    response.cookies.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                },\n                remove (name, options) {\n                    request.cookies.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                    response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request: {\n                            headers: request.headers\n                        }\n                    });\n                    response.cookies.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                }\n            }\n        });\n        let user = null;\n        try {\n            const { data, error } = await supabase.auth.getUser();\n            if (!error && data) {\n                user = data.user;\n            }\n        } catch (authError) {\n            // Silently handle auth errors and continue without blocking\n            console.warn('Auth check failed in middleware, continuing without auth:', authError);\n        }\n        // Define protected routes (including home page)\n        const protectedRoutes = [\n            '/',\n            '/post',\n            '/drafts',\n            '/profile',\n            '/notifications',\n            '/settings'\n        ];\n        // Define routes that should always be accessible (unprotected)\n        const alwaysAccessibleRoutes = [\n            '/auth/login',\n            '/auth/signup',\n            '/force-logout',\n            '/admin/clear-auth'\n        ];\n        // Check if current path is a protected route\n        const isProtectedRoute = protectedRoutes.some((route)=>route === '/' ? pathname === '/' : pathname.startsWith(route));\n        const isAuthRoute = authRoutes.some((route)=>pathname.startsWith(route));\n        const isAlwaysAccessible = alwaysAccessibleRoutes.some((route)=>pathname.startsWith(route));\n        // Always allow access to certain routes (like force-logout)\n        if (isAlwaysAccessible) {\n            return response;\n        }\n        // If user is not logged in and trying to access protected route\n        if (!user && isProtectedRoute) {\n            const redirectUrl = new URL('/auth/login', request.url);\n            redirectUrl.searchParams.set('redirectTo', pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        // If user is logged in and trying to access auth routes, redirect to home\n        if (user && isAuthRoute) {\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(new URL('/', request.url));\n        }\n        return response;\n    } catch (error) {\n        console.error('Middleware error:', error);\n        // Always return a response, never block the request\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    }\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ '/((?!api|_next/static|_next/image|favicon.ico).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});