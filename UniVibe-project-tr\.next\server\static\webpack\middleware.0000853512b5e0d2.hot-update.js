"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(middleware)/./node_modules/.pnpm/@supabase+ssr@0.6.1_@supabase+supabase-js@2.49.4/node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/.pnpm/next@15.2.4_react-dom@19.0.0_react@19.0.0__react@19.0.0/node_modules/next/dist/esm/api/server.js\");\n\n\nasync function middleware(request) {\n    try {\n        let response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n            request: {\n                headers: request.headers\n            }\n        });\n        // Skip middleware for static files and API routes\n        const { pathname } = request.nextUrl;\n        if (pathname.startsWith('/_next/') || pathname.startsWith('/api/') || pathname.includes('.')) {\n            return response;\n        }\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://wbzlnrekvqjkjvxkohwf.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndiemxucmVrdnFqa2p2eGtvaHdmIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDczNDE2NDksImV4cCI6MjA2MjkxNzY0OX0.ahMF1_RXeu-D6jJ6EF8TNzl-zJRSdy-yqwB4wWzL98Y\", {\n            cookies: {\n                get (name) {\n                    return request.cookies.get(name)?.value;\n                },\n                set (name, value, options) {\n                    request.cookies.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                    response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request: {\n                            headers: request.headers\n                        }\n                    });\n                    response.cookies.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                },\n                remove (name, options) {\n                    request.cookies.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                    response = next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next({\n                        request: {\n                            headers: request.headers\n                        }\n                    });\n                    response.cookies.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                }\n            }\n        });\n        let user = null;\n        try {\n            const { data, error } = await supabase.auth.getUser();\n            if (!error && data) {\n                user = data.user;\n            }\n        } catch (authError) {\n            // Silently handle auth errors and continue without blocking\n            console.warn('Auth check failed in middleware, continuing without auth:', authError);\n        }\n        // Define protected routes (including home page)\n        const protectedRoutes = [\n            '/',\n            '/post',\n            '/drafts',\n            '/profile',\n            '/notifications',\n            '/settings'\n        ];\n        // Define routes that should always be accessible (unprotected)\n        const alwaysAccessibleRoutes = [\n            '/auth/login',\n            '/auth/signup',\n            '/force-logout',\n            '/admin/clear-auth'\n        ];\n        // Check if current path is a protected route\n        const isProtectedRoute = protectedRoutes.some((route)=>route === '/' ? pathname === '/' : pathname.startsWith(route));\n        const isAlwaysAccessible = alwaysAccessibleRoutes.some((route)=>pathname.startsWith(route));\n        // Always allow access to unprotected routes (auth pages, logout, etc.)\n        if (isAlwaysAccessible) {\n            return response;\n        }\n        // If user is not logged in and trying to access protected route\n        if (!user && isProtectedRoute) {\n            const redirectUrl = new URL('/auth/login', request.url);\n            redirectUrl.searchParams.set('redirectTo', pathname);\n            return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.redirect(redirectUrl);\n        }\n        return response;\n    } catch (error) {\n        console.error('Middleware error:', error);\n        // Always return a response, never block the request\n        return next_server__WEBPACK_IMPORTED_MODULE_1__.NextResponse.next();\n    }\n}\nconst config = {\n    matcher: [\n        /*\n     * Match all request paths except for the ones starting with:\n     * - api (API routes)\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */ '/((?!api|_next/static|_next/image|favicon.ico).*)'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});