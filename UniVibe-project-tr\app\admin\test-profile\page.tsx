"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { createSupabaseBrowserClient, createUserProfile } from '@/lib/supabase'

export default function TestProfilePage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState('')
  const [error, setError] = useState('')
  const [testData, setTestData] = useState({
    email: '<EMAIL>',
    fullName: 'Test User',
    university: 'Test University',
    role: 'student' as const
  })
  const supabase = createSupabaseBrowserClient()

  const testProfileCreation = async () => {
    setLoading(true)
    setError('')
    setResult('')

    try {
      console.log('🧪 Starting profile creation test...')
      
      // First, get current auth state
      const { data: { user: currentUser }, error: authError } = await supabase.auth.getUser()
      console.log('🧪 Current auth user:', currentUser)
      
      if (authError) {
        setError(`Auth error: ${authError.message}`)
        return
      }

      if (!currentUser) {
        setError('No authenticated user found. Please sign up first.')
        return
      }

      // Create a mock user object for testing
      const mockUser = {
        id: currentUser.id,
        email: testData.email,
        user_metadata: {
          full_name: testData.fullName,
          university: testData.university
        }
      }

      console.log('🧪 Mock user object:', mockUser)

      // Test profile creation
      const { data, error: profileError } = await createUserProfile(
        mockUser,
        testData.role,
        {
          full_name: testData.fullName,
          university: testData.university
        }
      )

      if (profileError) {
        console.error('🧪 Profile creation failed:', profileError)
        setError(`Profile creation failed: ${profileError.message}`)
      } else {
        console.log('🧪 Profile creation succeeded:', data)
        setResult(`✅ Profile created successfully!\n\nData: ${JSON.stringify(data, null, 2)}`)
      }

    } catch (error) {
      console.error('🧪 Test error:', error)
      setError(`Test error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const testDirectInsert = async () => {
    setLoading(true)
    setError('')
    setResult('')

    try {
      console.log('🧪 Testing direct insert...')
      
      const { data: { user: currentUser } } = await supabase.auth.getUser()
      
      if (!currentUser) {
        setError('No authenticated user found. Please sign up first.')
        return
      }

      // Try direct insert
      const { data, error } = await supabase
        .from('users')
        .insert({
          id: currentUser.id,
          email: testData.email,
          full_name: testData.fullName,
          role: testData.role,
          university: testData.university
        })
        .select()
        .single()

      if (error) {
        console.error('🧪 Direct insert failed:', error)
        setError(`Direct insert failed: ${error.message}\nCode: ${error.code}\nDetails: ${error.details}`)
      } else {
        console.log('🧪 Direct insert succeeded:', data)
        setResult(`✅ Direct insert successful!\n\nData: ${JSON.stringify(data, null, 2)}`)
      }

    } catch (error) {
      console.error('🧪 Direct insert error:', error)
      setError(`Direct insert error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const checkCurrentUser = async () => {
    const { data: { user }, error } = await supabase.auth.getUser()
    console.log('Current user:', user)
    console.log('Auth error:', error)
    setResult(`Current user: ${JSON.stringify(user, null, 2)}`)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 p-6">
      <div className="max-w-2xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Profile Creation Test</CardTitle>
            <CardDescription>
              Test profile creation to debug the signup issue
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  value={testData.email}
                  onChange={(e) => setTestData(prev => ({ ...prev, email: e.target.value }))}
                />
              </div>
              <div>
                <Label htmlFor="fullName">Full Name</Label>
                <Input
                  id="fullName"
                  value={testData.fullName}
                  onChange={(e) => setTestData(prev => ({ ...prev, fullName: e.target.value }))}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="university">University</Label>
              <Input
                id="university"
                value={testData.university}
                onChange={(e) => setTestData(prev => ({ ...prev, university: e.target.value }))}
              />
            </div>

            <div className="flex gap-2">
              <Button onClick={checkCurrentUser} variant="outline">
                Check Current User
              </Button>
              <Button onClick={testProfileCreation} disabled={loading}>
                {loading ? 'Testing...' : 'Test Profile Creation'}
              </Button>
              <Button onClick={testDirectInsert} disabled={loading} variant="secondary">
                {loading ? 'Testing...' : 'Test Direct Insert'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {error && (
          <Alert variant="destructive">
            <AlertDescription className="whitespace-pre-wrap">{error}</AlertDescription>
          </Alert>
        )}

        {result && (
          <Alert>
            <AlertDescription className="whitespace-pre-wrap font-mono text-sm">{result}</AlertDescription>
          </Alert>
        )}

        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <ol className="list-decimal list-inside space-y-2 text-sm">
              <li>First, create an account using the signup page</li>
              <li>Then come back here and click "Check Current User" to see auth state</li>
              <li>Click "Test Profile Creation" to test the profile creation function</li>
              <li>Click "Test Direct Insert" to test direct database insertion</li>
              <li>Check the console for detailed debug logs</li>
            </ol>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
