"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseBrowserClient, createUserProfile } from '@/lib/supabase'

export default function AuthCallback() {
  const router = useRouter()
  const supabase = createSupabaseBrowserClient()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('Processing auth callback...')
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Auth callback session error:', error)
          router.push('/auth/login?error=callback_error')
          return
        }

        if (data.session?.user) {
          console.log('User session found, checking profile...')

          // Check if user profile exists
          const { data: existingProfile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', data.session.user.id)
            .single()

          if (profileError && profileError.code !== 'PGRST116') {
            console.error('Error checking profile:', profileError)
            router.push('/auth/login?error=profile_check_error')
            return
          }

          // Create profile if it doesn't exist
          if (!existingProfile) {
            console.log('Profile not found, creating new profile...')
            const { data: newProfile, error: createError } = await createUserProfile(data.session.user)

            if (createError) {
              console.error('Profile creation failed in callback:', createError)
              router.push('/auth/login?error=profile_creation_failed')
              return
            }

            console.log('Profile created successfully in callback')
          } else {
            console.log('Existing profile found')
          }

          // Redirect to intended page or home
          const redirectTo = new URLSearchParams(window.location.search).get('redirectTo') || '/'
          console.log('Redirecting to:', redirectTo)
          router.push(redirectTo)
        } else {
          console.log('No user session found, redirecting to login')
          router.push('/auth/login')
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error)
        router.push('/auth/login?error=unexpected_error')
      }
    }

    handleAuthCallback()
  }, [router, supabase])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center">
      <div className="text-center space-y-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent mx-auto"></div>
        <h2 className="text-xl font-semibold">Completing sign in...</h2>
        <p className="text-muted-foreground">Please wait while we set up your account.</p>
      </div>
    </div>
  )
}
