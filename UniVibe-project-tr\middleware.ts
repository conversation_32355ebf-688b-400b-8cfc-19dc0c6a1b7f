import { createServerClient } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'

export async function middleware(request: NextRequest) {
  try {
    let response = NextResponse.next({
      request: {
        headers: request.headers,
      },
    })

    // Skip middleware for static files and API routes
    const { pathname } = request.nextUrl
    if (
      pathname.startsWith('/_next/') ||
      pathname.startsWith('/api/') ||
      pathname.includes('.')
    ) {
      return response
    }

    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return request.cookies.get(name)?.value
          },
          set(name: string, value: string, options: any) {
            request.cookies.set({
              name,
              value,
              ...options,
            })
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            })
            response.cookies.set({
              name,
              value,
              ...options,
            })
          },
          remove(name: string, options: any) {
            request.cookies.set({
              name,
              value: '',
              ...options,
            })
            response = NextResponse.next({
              request: {
                headers: request.headers,
              },
            })
            response.cookies.set({
              name,
              value: '',
              ...options,
            })
          },
        },
      }
    )

    let user = null
    try {
      const { data, error } = await supabase.auth.getUser()
      if (!error && data) {
        user = data.user
      }
    } catch (authError) {
      // Silently handle auth errors and continue without blocking
      console.warn('Auth check failed in middleware, continuing without auth:', authError)
    }

    // Define protected routes (including home page)
    const protectedRoutes = [
      '/',
      '/post',
      '/drafts',
      '/profile',
      '/notifications',
      '/settings'
    ]

    // Define routes that should always be accessible (unprotected)
    const alwaysAccessibleRoutes = [
      '/auth/login',
      '/auth/signup',
      '/force-logout',
      '/admin/clear-auth'
    ]

    // Check if current path is a protected route
    const isProtectedRoute = protectedRoutes.some(route =>
      route === '/' ? pathname === '/' : pathname.startsWith(route)
    )
    const isAlwaysAccessible = alwaysAccessibleRoutes.some(route => pathname.startsWith(route))

    // Always allow access to unprotected routes (auth pages, logout, etc.)
    if (isAlwaysAccessible) {
      return response
    }

    // If user is not logged in and trying to access protected route
    if (!user && isProtectedRoute) {
      const redirectUrl = new URL('/auth/login', request.url)
      redirectUrl.searchParams.set('redirectTo', pathname)
      return NextResponse.redirect(redirectUrl)
    }

    return response
  } catch (error) {
    console.error('Middleware error:', error)
    // Always return a response, never block the request
    return NextResponse.next()
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
