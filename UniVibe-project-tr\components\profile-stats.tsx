"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { getUserEventStats, getUserFavoriteCategories } from '@/lib/supabase'
import { useAuth } from '@/lib/auth-context'
import { 
  Calendar, 
  Trophy, 
  Users, 
  Star, 
  TrendingUp, 
  Heart,
  Sparkles,
  Target
} from 'lucide-react'

interface EventStats {
  eventsCreated: number
  eventsAttended: number
  upcomingEvents: number
  totalRSVPs: number
}

export function ProfileStats() {
  const { user, profile } = useAuth()
  const [stats, setStats] = useState<EventStats | null>(null)
  const [favoriteCategories, setFavoriteCategories] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user?.id) {
      loadStats()
      loadFavoriteCategories()
    }
  }, [user?.id])

  const loadStats = async () => {
    if (!user?.id) return

    try {
      const { data, error } = await getUserEventStats(user.id)
      if (!error && data) {
        setStats(data)
      }
    } catch (error) {
      console.error('Error loading stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadFavoriteCategories = async () => {
    if (!user?.id) return

    try {
      const { data, error } = await getUserFavoriteCategories(user.id)
      if (!error && data) {
        setFavoriteCategories(data.map(item => item.category))
      }
    } catch (error) {
      console.error('Error loading favorite categories:', error)
    }
  }

  const getProfileCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-500'
    if (percentage >= 60) return 'text-yellow-500'
    if (percentage >= 40) return 'text-orange-500'
    return 'text-red-500'
  }

  const getProfileCompletionMessage = (percentage: number) => {
    if (percentage >= 90) return "Amazing profile! 🌟"
    if (percentage >= 70) return "Great profile! 🎉"
    if (percentage >= 50) return "Good start! 👍"
    if (percentage >= 30) return "Keep going! 💪"
    return "Let's get started! 🚀"
  }

  const profileCompletion = profile?.profile_completion_percentage || 0

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Profile Completion */}
      <Card className="glass-effect border-primary/20">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Target className="h-5 w-5 text-primary" />
            Profile Completion
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">
                {getProfileCompletionMessage(profileCompletion)}
              </span>
              <span className={`text-sm font-bold ${getProfileCompletionColor(profileCompletion)}`}>
                {profileCompletion}%
              </span>
            </div>
            <Progress 
              value={profileCompletion} 
              className="h-2"
            />
            <p className="text-xs text-muted-foreground">
              Complete your profile to get better event recommendations!
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Events Created */}
        <Card className="glass-effect hover:neon-glow transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Events Created</p>
                <p className="text-2xl font-bold text-primary">{stats?.eventsCreated || 0}</p>
              </div>
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                <Sparkles className="h-6 w-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Events Attended */}
        <Card className="glass-effect hover:neon-glow transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Events Attended</p>
                <p className="text-2xl font-bold text-accent">{stats?.eventsAttended || 0}</p>
              </div>
              <div className="h-12 w-12 bg-accent/10 rounded-full flex items-center justify-center">
                <Trophy className="h-6 w-6 text-accent" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Events */}
        <Card className="glass-effect hover:neon-glow transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Upcoming Events</p>
                <p className="text-2xl font-bold text-green-500">{stats?.upcomingEvents || 0}</p>
              </div>
              <div className="h-12 w-12 bg-green-500/10 rounded-full flex items-center justify-center">
                <Calendar className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total RSVPs */}
        <Card className="glass-effect hover:neon-glow transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total RSVPs</p>
                <p className="text-2xl font-bold text-purple-500">{stats?.totalRSVPs || 0}</p>
              </div>
              <div className="h-12 w-12 bg-purple-500/10 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Favorite Categories */}
      {favoriteCategories.length > 0 && (
        <Card className="glass-effect">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-accent" />
              Favorite Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {favoriteCategories.map((category) => (
                <Badge 
                  key={category} 
                  variant="secondary" 
                  className="bg-gradient-to-r from-primary/20 to-accent/20 hover:from-primary/30 hover:to-accent/30 transition-all"
                >
                  {category}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Achievement Badges */}
      <Card className="glass-effect">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-yellow-500" />
            Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {/* Event Creator Badge */}
            {(stats?.eventsCreated || 0) >= 1 && (
              <div className="text-center p-3 rounded-lg bg-primary/10 border border-primary/20">
                <Sparkles className="h-8 w-8 text-primary mx-auto mb-2" />
                <p className="text-xs font-medium">Event Creator</p>
              </div>
            )}

            {/* Social Butterfly Badge */}
            {(stats?.eventsAttended || 0) >= 5 && (
              <div className="text-center p-3 rounded-lg bg-accent/10 border border-accent/20">
                <Users className="h-8 w-8 text-accent mx-auto mb-2" />
                <p className="text-xs font-medium">Social Butterfly</p>
              </div>
            )}

            {/* Profile Master Badge */}
            {profileCompletion >= 80 && (
              <div className="text-center p-3 rounded-lg bg-green-500/10 border border-green-500/20">
                <Target className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <p className="text-xs font-medium">Profile Master</p>
              </div>
            )}

            {/* Trendsetter Badge */}
            {favoriteCategories.length >= 3 && (
              <div className="text-center p-3 rounded-lg bg-purple-500/10 border border-purple-500/20">
                <TrendingUp className="h-8 w-8 text-purple-500 mx-auto mb-2" />
                <p className="text-xs font-medium">Trendsetter</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
