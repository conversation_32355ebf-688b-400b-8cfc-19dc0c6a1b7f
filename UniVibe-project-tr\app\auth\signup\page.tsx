"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { signInWithGoogle, createSupabaseBrowserClient, createUserProfile, UserRole } from '@/lib/supabase'
import { useAuth } from '@/lib/auth-context'
import { Sparkles, Mail, Lock, User, GraduationCap, ArrowRight, AlertCircle } from 'lucide-react'
import Link from 'next/link'

export default function SignupPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    fullName: '',
    university: '',
    role: 'student' as User<PERSON><PERSON>
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const router = useRouter()
  const { user } = useAuth()
  const supabase = createSupabaseBrowserClient()

  // Note: Signup page is now unprotected for easier navigation
  // Users can access this page even when logged in

  const handleEmailSignup = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      console.log('Starting email signup process...')
      const { data, error } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
        options: {
          data: {
            full_name: formData.fullName,
            university: formData.university,
            role: formData.role,
          }
        }
      })

      if (error) {
        console.error('Supabase auth signup error:', error)
        setError(`Signup failed: ${error.message}`)
        return
      }

      if (!data.user) {
        setError('Signup completed but no user data received. Please try signing in.')
        return
      }

      console.log('✅ User created in auth successfully!')
      console.log('User ID:', data.user.id)
      console.log('User Email:', data.user.email)

      // Wait a moment for auth context to be established
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('Creating user profile...')

      // Create user profile using API route (bypasses RLS issues)
      let profileData = null
      let profileError = null
      let retryCount = 0
      const maxRetries = 3

      while (retryCount < maxRetries && !profileData) {
        try {
          console.log(`Profile creation attempt ${retryCount + 1}...`)

          const response = await fetch('/api/auth/create-profile', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              userId: data.user.id,
              email: data.user.email,
              fullName: formData.fullName,
              university: formData.university,
              role: formData.role
            })
          })

          const result = await response.json()

          if (!response.ok) {
            profileError = result.error || new Error(`HTTP ${response.status}`)
            console.error(`Profile creation attempt ${retryCount + 1} failed:`)
            console.error('API Error:', result)
            retryCount++

            if (retryCount < maxRetries) {
              console.log(`Retrying profile creation in 2 seconds... (${retryCount}/${maxRetries})`)
              await new Promise(resolve => setTimeout(resolve, 2000))
            }
          } else {
            profileData = result.data
            console.log('✅ Profile created successfully via API!')
            break
          }
        } catch (createError) {
          console.error(`Profile creation attempt ${retryCount + 1} exception:`)
          console.error('Exception details:', {
            message: createError?.message,
            name: createError?.name,
            stack: createError?.stack,
            toString: createError?.toString()
          })
          retryCount++

          if (retryCount < maxRetries) {
            console.log(`Retrying profile creation in 2 seconds... (${retryCount}/${maxRetries})`)
            await new Promise(resolve => setTimeout(resolve, 2000))
          } else {
            profileError = createError
          }
        }
      }

      if (profileError && retryCount >= maxRetries) {
        console.error('🚨 Profile creation failed after all retries:')
        console.error('Final error details:', {
          message: profileError?.message,
          code: profileError?.code,
          details: profileError?.details,
          hint: profileError?.hint,
          name: profileError?.name,
          stack: profileError?.stack
        })

        setError(`Account created successfully, but there was an issue setting up your profile.

Error: ${profileError?.message || profileError?.toString() || 'Unknown error'}

Don't worry! Your account exists and you can try signing in. The profile will be created automatically when you sign in.`)

        // Don't sign out the user, let them try to sign in
        setLoading(false)
        return
      }

      console.log('✅ Account and profile created successfully!')
      console.log('✅ Profile data:', profileData)

      // Show success message
      setError('')
      const successMessage = `🎉 Welcome to UniVibe, ${formData.fullName}! Your account has been created successfully. Redirecting to home page...`
      setSuccess(successMessage)
      console.log(successMessage)

      // Small delay to show success message and ensure auth context is updated
      await new Promise(resolve => setTimeout(resolve, 2000))

      console.log('✅ Redirecting to home page...')
      // Redirect to home page
      router.push('/')

    } catch (error) {
      console.error('Unexpected signup error:', error)
      setError(`An unexpected error occurred: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const handleGoogleSignup = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      const { error } = await signInWithGoogle()
      if (error) {
        setError(error.message)
        setLoading(false)
      }
      // Don't set loading to false here as we're redirecting
    } catch (error) {
      setError('Failed to sign up with Google. Please try again.')
      setLoading(false)
    }
  }

  if (user) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo and Welcome */}
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2">
            <div className="p-3 rounded-2xl bg-gradient-to-br from-primary to-accent">
              <Sparkles className="h-8 w-8 text-white" />
            </div>
            <h1 className="text-3xl font-bold gradient-text">UniVibe</h1>
          </div>
          <div className="space-y-2">
            <h2 className="text-2xl font-bold">Join the community! 🚀</h2>
            <p className="text-muted-foreground">
              Create your account to start discovering and organizing amazing campus events
            </p>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="whitespace-pre-line text-sm leading-relaxed">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Success Alert */}
        {success && (
          <Alert className="mb-6 border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200">
            <Sparkles className="h-4 w-4" />
            <AlertDescription className="text-sm leading-relaxed">
              {success}
            </AlertDescription>
          </Alert>
        )}

        {/* Signup Card */}
        <Card className="shadow-2xl border-white/10 bg-gradient-to-br from-card/80 to-card/60 backdrop-blur-sm">
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className="text-xl">Create your account</CardTitle>
            <CardDescription>
              Get started with your preferred sign up method
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Google Sign Up - Temporarily Disabled */}
            {/*
            <Button
              onClick={handleGoogleSignup}
              disabled={loading}
              className="w-full h-12 bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-sm"
              variant="outline"
            >
              Continue with Google
            </Button>

            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <Separator className="w-full" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-card px-2 text-muted-foreground">Or continue with email</span>
              </div>
            </div>
            */}

            {/* Email Form */}
            <form onSubmit={handleEmailSignup} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="fullName"
                      type="text"
                      placeholder="John Doe"
                      value={formData.fullName}
                      onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">I am a</Label>
                  <Select value={formData.role} onValueChange={(value: UserRole) => setFormData(prev => ({ ...prev, role: value }))}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="student">🎓 Student</SelectItem>
                      <SelectItem value="organizer">🎯 Organizer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="university">University</Label>
                <div className="relative">
                  <GraduationCap className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="university"
                    type="text"
                    placeholder="Your University Name"
                    value={formData.university}
                    onChange={(e) => setFormData(prev => ({ ...prev, university: e.target.value }))}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    id="password"
                    type="password"
                    placeholder="Create a strong password"
                    value={formData.password}
                    onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                    className="pl-10"
                    required
                  />
                </div>
              </div>
              
              <Button
                type="submit"
                disabled={loading}
                className="w-full h-12 bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
              >
                {loading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <ArrowRight className="h-4 w-4 mr-2" />
                )}
                Create Account
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Sign In Link */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            Already have an account?{' '}
            <Link href="/auth/login" className="text-accent hover:underline font-medium">
              Sign in here
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
