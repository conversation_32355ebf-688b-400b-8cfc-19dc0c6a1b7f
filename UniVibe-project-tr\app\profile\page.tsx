"use client"

import { useState, useEffect } from 'react'
import { AuthGuard } from "@/components/auth-guard"
import { <PERSON>ton } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ProfileEditModal } from "@/components/profile-edit-modal"
import { ProfileStats } from "@/components/profile-stats"
import { useAuth } from "@/lib/auth-context"
import { getEventsByUser, getUserFavoriteCategories } from "@/lib/supabase"
import {
  Edit,
  LogOut,
  Calendar,
  MapPin,
  User,
  Mail,
  Phone,
  Globe,
  Instagram,
  Twitter,
  Linkedin,
  Github,
  Heart,
  Sparkles,
  GraduationCap,
  Clock
} from "lucide-react"
import Link from "next/link"
import { format } from "date-fns"

const socialIcons = {
  instagram: Instagram,
  twitter: Twitter,
  linkedin: Linkedin,
  github: Github,
  website: Globe
}

export default function ProfilePage() {
  const { user, profile, signOut } = useAuth()
  const [editModalOpen, setEditModalOpen] = useState(false)
  const [userEvents, setUserEvents] = useState<any[]>([])
  const [favoriteCategories, setFavoriteCategories] = useState<string[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (user?.id) {
      loadUserData()
    }
  }, [user?.id])

  const loadUserData = async () => {
    if (!user?.id) return

    try {
      // Load user's events (both created and RSVPed)
      const { data: events } = await getEventsByUser(user.id)
      if (events) {
        setUserEvents(events)
      }

      // Load favorite categories
      const { data: categories } = await getUserFavoriteCategories(user.id)
      if (categories) {
        setFavoriteCategories(categories.map(c => c.category))
      }
    } catch (error) {
      console.error('Error loading user data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'PPP')
    } catch {
      return dateString
    }
  }

  const getSocialLinks = () => {
    if (!profile?.social_links) return []

    const links = profile.social_links as Record<string, string>
    return Object.entries(links)
      .filter(([_, url]) => url && url.trim() !== '')
      .map(([platform, url]) => ({
        platform,
        url,
        icon: socialIcons[platform as keyof typeof socialIcons] || Globe
      }))
  }

  if (loading) {
    return (
      <AuthGuard requireAuth={true}>
        <div className="p-4 space-y-6">
          <Card className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="h-20 w-20 bg-muted rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-6 bg-muted rounded w-1/3"></div>
                  <div className="h-4 bg-muted rounded w-1/2"></div>
                  <div className="h-4 bg-muted rounded w-1/4"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AuthGuard>
    )
  }

  return (
    <AuthGuard requireAuth={true}>
      <div className="p-4 space-y-6">
        {/* Profile Header */}
        <Card className="glass-effect border-primary/20">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
              {/* Avatar and Basic Info */}
              <div className="flex items-center gap-4 flex-1">
                <Avatar className="h-24 w-24 border-4 border-primary/20">
                  <AvatarImage src={profile?.avatar_url || "/placeholder.svg"} />
                  <AvatarFallback className="text-xl gradient-text">
                    {profile?.full_name ? getInitials(profile.full_name) : 'U'}
                  </AvatarFallback>
                </Avatar>

                <div className="space-y-2">
                  <h1 className="text-2xl font-bold gradient-text">
                    {profile?.full_name || 'Anonymous User'} ✨
                  </h1>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <span>{user?.email}</span>
                    </div>
                    {profile?.university && (
                      <div className="flex items-center gap-2">
                        <GraduationCap className="h-4 w-4" />
                        <span>{profile.university}</span>
                      </div>
                    )}
                    {profile?.location && (
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span>{profile.location}</span>
                      </div>
                    )}
                    {profile?.created_at && (
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>Member since {formatDate(profile.created_at)}</span>
                      </div>
                    )}
                  </div>

                  {/* Role Badge */}
                  <Badge
                    variant="secondary"
                    className="bg-gradient-to-r from-primary/20 to-accent/20"
                  >
                    {profile?.role === 'organizer' ? '🎯 Organizer' : '🎓 Student'}
                  </Badge>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button
                  onClick={() => setEditModalOpen(true)}
                  className="bg-gradient-to-r from-primary to-accent hover:opacity-90"
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
                <Button variant="outline" onClick={signOut}>
                  <LogOut className="h-4 w-4 mr-2" />
                  Sign Out
                </Button>
              </div>
            </div>

            {/* Bio */}
            {profile?.bio && (
              <>
                <Separator className="my-4" />
                <p className="text-muted-foreground leading-relaxed">
                  {profile.bio}
                </p>
              </>
            )}

            {/* Social Links */}
            {getSocialLinks().length > 0 && (
              <>
                <Separator className="my-4" />
                <div className="flex items-center gap-4">
                  <span className="text-sm font-medium text-muted-foreground">Connect:</span>
                  <div className="flex gap-2">
                    {getSocialLinks().map(({ platform, url, icon: Icon }) => (
                      <Button
                        key={platform}
                        variant="outline"
                        size="icon"
                        asChild
                        className="hover:bg-primary/10"
                      >
                        <a href={url} target="_blank" rel="noopener noreferrer">
                          <Icon className="h-4 w-4" />
                        </a>
                      </Button>
                    ))}
                  </div>
                </div>
              </>
            )}

            {/* Interests */}
            {profile?.interests && profile.interests.length > 0 && (
              <>
                <Separator className="my-4" />
                <div>
                  <h3 className="text-sm font-medium mb-2 flex items-center gap-2">
                    <Sparkles className="h-4 w-4 text-accent" />
                    Interests
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {profile.interests.map((interest) => (
                      <Badge key={interest} variant="outline">
                        {interest}
                      </Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Profile Stats */}
        <ProfileStats />

        {/* Events Tabs */}
        <Tabs defaultValue="upcoming" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upcoming">📅 Upcoming Events</TabsTrigger>
            <TabsTrigger value="past">🏆 Past Events</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="space-y-3">
            {userEvents.filter(event => new Date(event.date) >= new Date()).length > 0 ? (
              userEvents
                .filter(event => new Date(event.date) >= new Date())
                .map((event) => (
                  <Link key={event.id} href={`/event/${event.id}`}>
                    <Card className="hover:shadow-md transition-all hover:neon-glow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold">{event.title}</h3>
                          <Badge variant="outline">{event.category}</Badge>
                        </div>
                        <div className="space-y-1 text-sm text-muted-foreground">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(event.date)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4" />
                            <span>{event.location}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </Link>
                ))
            ) : (
              <Card className="glass-effect">
                <CardContent className="p-8 text-center">
                  <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No upcoming events</h3>
                  <p className="text-muted-foreground mb-4">
                    Discover amazing events happening around you!
                  </p>
                  <Button asChild className="bg-gradient-to-r from-primary to-accent">
                    <Link href="/">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="past" className="space-y-3">
            {userEvents.filter(event => new Date(event.date) < new Date()).length > 0 ? (
              userEvents
                .filter(event => new Date(event.date) < new Date())
                .map((event) => (
                  <Card key={event.id} className="opacity-75 hover:opacity-100 transition-opacity">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-semibold">{event.title}</h3>
                        <Badge variant="secondary">{event.category}</Badge>
                      </div>
                      <div className="space-y-1 text-sm text-muted-foreground">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(event.date)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          <span>{event.location}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
            ) : (
              <Card className="glass-effect">
                <CardContent className="p-8 text-center">
                  <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No past events yet</h3>
                  <p className="text-muted-foreground">
                    Your event history will appear here once you start attending events.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>

        {/* Profile Edit Modal */}
        <ProfileEditModal
          open={editModalOpen}
          onOpenChange={setEditModalOpen}
        />
      </div>
    </AuthGuard>
  )
}
