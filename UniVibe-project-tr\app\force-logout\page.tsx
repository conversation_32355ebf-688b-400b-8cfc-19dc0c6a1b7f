"use client"

import { useEffect, useState } from 'react'
import { forceSignOut } from '@/lib/supabase'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function ForceLogoutPage() {
  const [status, setStatus] = useState<'idle' | 'logging-out' | 'success' | 'error'>('idle')
  const [message, setMessage] = useState('')

  const handleForceLogout = async () => {
    setStatus('logging-out')
    setMessage('Clearing all authentication data...')
    
    try {
      // Force sign out
      await forceSignOut()
      
      // Clear additional browser data
      if (typeof window !== 'undefined') {
        // Clear all localStorage
        localStorage.clear()
        sessionStorage.clear()
        
        // Clear all cookies
        document.cookie.split(";").forEach((c) => {
          const eqPos = c.indexOf("=")
          const name = eqPos > -1 ? c.substr(0, eqPos) : c
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=." + window.location.hostname
        })
        
        // Clear IndexedDB (Supabase might use this)
        if ('indexedDB' in window) {
          indexedDB.databases().then(databases => {
            databases.forEach(db => {
              if (db.name) {
                indexedDB.deleteDatabase(db.name)
              }
            })
          }).catch(console.error)
        }
      }
      
      setStatus('success')
      setMessage('All authentication data cleared successfully!')
      
      // Redirect after a short delay
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 2000)
      
    } catch (error) {
      console.error('Force logout error:', error)
      setStatus('error')
      setMessage('Error during logout, but redirecting anyway...')
      
      // Redirect even if there's an error
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 2000)
    }
  }

  useEffect(() => {
    // Auto-trigger logout when page loads
    handleForceLogout()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Force Logout</CardTitle>
          <CardDescription>
            Clearing all authentication data and redirecting to login
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center">
            {status === 'logging-out' && (
              <div className="space-y-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                <p className="text-sm text-muted-foreground">{message}</p>
              </div>
            )}
            
            {status === 'success' && (
              <div className="space-y-4">
                <div className="text-green-500 text-4xl">✓</div>
                <p className="text-sm text-green-600">{message}</p>
                <p className="text-xs text-muted-foreground">Redirecting to login...</p>
              </div>
            )}
            
            {status === 'error' && (
              <div className="space-y-4">
                <div className="text-red-500 text-4xl">⚠</div>
                <p className="text-sm text-red-600">{message}</p>
                <p className="text-xs text-muted-foreground">Redirecting to login...</p>
              </div>
            )}
            
            {status === 'idle' && (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">Click the button below to force logout and clear all cached authentication data.</p>
                <Button onClick={handleForceLogout} className="w-full">
                  Force Logout
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
