import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Generated Database Types
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      events: {
        Row: {
          attendees_count: number | null
          category: string
          created_at: string | null
          date: string
          description: string | null
          id: string
          image_url: string | null
          location: string
          organizer: string
          organizer_id: string | null
          time: string
          title: string
          updated_at: string | null
        }
        Insert: {
          attendees_count?: number | null
          category: string
          created_at?: string | null
          date: string
          description?: string | null
          id?: string
          image_url?: string | null
          location: string
          organizer: string
          organizer_id?: string | null
          time: string
          title: string
          updated_at?: string | null
        }
        Update: {
          attendees_count?: number | null
          category?: string
          created_at?: string | null
          date?: string
          description?: string | null
          id?: string
          image_url?: string | null
          location?: string
          organizer?: string
          organizer_id?: string | null
          time?: string
          title?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "events_organizer_id_fkey"
            columns: ["organizer_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          created_at: string | null
          id: string
          message: string
          read: boolean | null
          title: string
          type: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          id?: string
          message: string
          read?: boolean | null
          title: string
          type?: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          id?: string
          message?: string
          read?: boolean | null
          title?: string
          type?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      rsvps: {
        Row: {
          created_at: string | null
          event_id: string
          id: string
          user_id: string
        }
        Insert: {
          created_at?: string | null
          event_id: string
          id?: string
          user_id: string
        }
        Update: {
          created_at?: string | null
          event_id?: string
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "rsvps_event_id_fkey"
            columns: ["event_id"]
            isOneToOne: false
            referencedRelation: "events"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "rsvps_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      users: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          date_of_birth: string | null
          email: string
          full_name: string | null
          id: string
          interests: string[] | null
          location: string | null
          phone: string | null
          profile_completion_percentage: number | null
          role: string
          social_links: Json | null
          university: string | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email: string
          full_name?: string | null
          id?: string
          interests?: string[] | null
          location?: string | null
          phone?: string | null
          profile_completion_percentage?: number | null
          role?: string
          social_links?: Json | null
          university?: string | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          date_of_birth?: string | null
          email?: string
          full_name?: string | null
          id?: string
          interests?: string[] | null
          location?: string | null
          phone?: string | null
          profile_completion_percentage?: number | null
          role?: string
          social_links?: Json | null
          university?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      user_favorite_categories: {
        Row: {
          category: string
          created_at: string | null
          id: string
          user_id: string
        }
        Insert: {
          category: string
          created_at?: string | null
          id?: string
          user_id: string
        }
        Update: {
          category?: string
          created_at?: string | null
          id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_favorite_categories_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey)

// Create browser client for auth
export function createSupabaseBrowserClient() {
  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey)
}

// User role types
export type UserRole = 'student' | 'organizer'

// Auth helper functions
export async function signInWithGoogle() {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: 'google',
    options: {
      redirectTo: `${window.location.origin}/auth/callback`,
    },
  })
  return { data, error }
}

export async function signOut() {
  const { error } = await supabase.auth.signOut()
  return { error }
}

export async function forceSignOut() {
  try {
    // Sign out from Supabase
    await supabase.auth.signOut()

    // Clear all local storage
    if (typeof window !== 'undefined') {
      localStorage.clear()
      sessionStorage.clear()

      // Clear all cookies for this domain
      document.cookie.split(";").forEach((c) => {
        const eqPos = c.indexOf("=")
        const name = eqPos > -1 ? c.substr(0, eqPos) : c
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
        document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname
      })
    }

    return { error: null }
  } catch (error) {
    console.error('Force sign out error:', error)
    return { error }
  }
}

export async function getCurrentUser() {
  const { data: { user }, error } = await supabase.auth.getUser()
  return { user, error }
}

export async function getUserProfile(userId: string) {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('id', userId)
    .single()
  return { data, error }
}

export async function updateUserProfile(userId: string, updates: Partial<Database['public']['Tables']['users']['Update']>) {
  const { data, error } = await supabase
    .from('users')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()
  return { data, error }
}

export async function createUserProfile(user: any, role: UserRole = 'student', additionalData?: { full_name?: string; university?: string }) {
  // Handle both OAuth users (with user_metadata) and email signup users
  const fullName = additionalData?.full_name ||
                   user.user_metadata?.full_name ||
                   user.user_metadata?.name ||
                   user.email?.split('@')[0] ||
                   'User'

  const university = additionalData?.university || user.user_metadata?.university || null

  const profileData = {
    id: user.id,
    email: user.email,
    full_name: fullName,
    avatar_url: user.user_metadata?.avatar_url || null,
    role: role,
    university: university,
  }

  console.log('🔍 DEBUG: Creating user profile...')
  console.log('🔍 User object received:', {
    id: user.id,
    email: user.email,
    user_metadata: user.user_metadata,
    aud: user.aud,
    role: user.role
  })
  console.log('🔍 Additional data:', additionalData)
  console.log('🔍 Profile data to insert:', profileData)
  console.log('🔍 Current auth user:', await supabase.auth.getUser())

  // First, let's check if the user already exists
  const { data: existingUser, error: checkError } = await supabase
    .from('users')
    .select('*')
    .eq('id', user.id)
    .single()

  if (checkError && checkError.code !== 'PGRST116') {
    console.error('🚨 Error checking existing user:', checkError)
  } else if (existingUser) {
    console.log('⚠️ User already exists:', existingUser)
    return { data: existingUser, error: null }
  }

  console.log('✅ User does not exist, proceeding with insert...')

  const { data, error } = await supabase
    .from('users')
    .insert(profileData)
    .select()
    .single()

  if (error) {
    console.error('🚨 Profile creation error details:', {
      code: error.code,
      message: error.message,
      details: error.details,
      hint: error.hint,
      user_id: user.id,
      user_email: user.email,
      profileData: profileData
    })

    // Let's also check the current session
    const { data: session } = await supabase.auth.getSession()
    console.error('🚨 Current session during error:', session)
  } else {
    console.log('✅ Profile created successfully:', data)
  }

  return { data, error }
}

// Get user's favorite categories
export async function getUserFavoriteCategories(userId: string) {
  const { data, error } = await supabase
    .from('user_favorite_categories')
    .select('category')
    .eq('user_id', userId)
  return { data, error }
}

// Add favorite category
export async function addFavoriteCategory(userId: string, category: string) {
  const { data, error } = await supabase
    .from('user_favorite_categories')
    .insert({ user_id: userId, category })
    .select()
    .single()
  return { data, error }
}

// Remove favorite category
export async function removeFavoriteCategory(userId: string, category: string) {
  const { error } = await supabase
    .from('user_favorite_categories')
    .delete()
    .eq('user_id', userId)
    .eq('category', category)
  return { error }
}

// Get user's event statistics
export async function getUserEventStats(userId: string) {
  // Get events created by user
  const { data: createdEvents, error: createdError } = await supabase
    .from('events')
    .select('id')
    .eq('organizer_id', userId)

  // Get events user has RSVPed to
  const { data: rsvpEvents, error: rsvpError } = await supabase
    .from('rsvps')
    .select('event_id, events!inner(date)')
    .eq('user_id', userId)

  if (createdError || rsvpError) {
    return { data: null, error: createdError || rsvpError }
  }

  // Count upcoming vs past events
  const now = new Date().toISOString().split('T')[0]
  const upcomingEvents = rsvpEvents?.filter(rsvp => rsvp.events.date >= now) || []
  const pastEvents = rsvpEvents?.filter(rsvp => rsvp.events.date < now) || []

  return {
    data: {
      eventsCreated: createdEvents?.length || 0,
      eventsAttended: pastEvents.length,
      upcomingEvents: upcomingEvents.length,
      totalRSVPs: rsvpEvents?.length || 0
    },
    error: null
  }
}

// Get events by user (both created and RSVPed)
export async function getEventsByUser(userId: string) {
  // Get events created by user
  const { data: createdEvents, error: createdError } = await supabase
    .from('events')
    .select('*')
    .eq('organizer_id', userId)

  // Get events user has RSVPed to
  const { data: rsvpData, error: rsvpError } = await supabase
    .from('rsvps')
    .select('events(*)')
    .eq('user_id', userId)

  if (createdError || rsvpError) {
    return { data: null, error: createdError || rsvpError }
  }

  // Combine and deduplicate events
  const rsvpEvents = rsvpData?.map(rsvp => rsvp.events).filter(Boolean) || []
  const allEvents = [...(createdEvents || []), ...rsvpEvents]

  // Remove duplicates based on event ID
  const uniqueEvents = allEvents.filter((event, index, self) =>
    index === self.findIndex(e => e.id === event.id)
  )

  // Sort by date (newest first)
  uniqueEvents.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

  return { data: uniqueEvents, error: null }
}
