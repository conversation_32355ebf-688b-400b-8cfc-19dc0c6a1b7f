"use client"

import { useState, useEffect } from 'react'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { updateUserProfile, addFavoriteCategory, removeFavoriteCategory, getUserFavoriteCategories } from '@/lib/supabase'
import { useAuth } from '@/lib/auth-context'
import { 
  User, 
  Mail, 
  MapPin, 
  Phone, 
  Calendar, 
  Heart, 
  Plus, 
  X, 
  Save, 
  Loader2,
  Instagram,
  Twitter,
  Linkedin,
  Github,
  Globe
} from 'lucide-react'
import { toast } from 'sonner'

interface ProfileEditModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

const availableCategories = [
  'Music', 'Academic', 'Sports', 'Technology', 'Arts', 'Food', 
  'Gaming', 'Networking', 'Workshop', 'Social', 'Career', 'Health'
]

const socialPlatforms = [
  { key: 'instagram', label: 'Instagram', icon: Instagram, placeholder: '@username' },
  { key: 'twitter', label: 'Twitter', icon: Twitter, placeholder: '@username' },
  { key: 'linkedin', label: 'LinkedIn', icon: Linkedin, placeholder: 'linkedin.com/in/username' },
  { key: 'github', label: 'GitHub', icon: Github, placeholder: 'github.com/username' },
  { key: 'website', label: 'Website', icon: Globe, placeholder: 'https://yourwebsite.com' }
]

export function ProfileEditModal({ open, onOpenChange }: ProfileEditModalProps) {
  const { user, profile, refreshProfile } = useAuth()
  const [loading, setLoading] = useState(false)
  const [favoriteCategories, setFavoriteCategories] = useState<string[]>([])
  const [newCategory, setNewCategory] = useState('')
  
  const [formData, setFormData] = useState({
    full_name: '',
    bio: '',
    university: '',
    location: '',
    phone: '',
    date_of_birth: '',
    interests: [] as string[],
    social_links: {} as Record<string, string>
  })

  // Load user data when modal opens
  useEffect(() => {
    if (open && profile) {
      setFormData({
        full_name: profile.full_name || '',
        bio: profile.bio || '',
        university: profile.university || '',
        location: profile.location || '',
        phone: profile.phone || '',
        date_of_birth: profile.date_of_birth || '',
        interests: profile.interests || [],
        social_links: (profile.social_links as Record<string, string>) || {}
      })
      
      // Load favorite categories
      if (user?.id) {
        loadFavoriteCategories()
      }
    }
  }, [open, profile, user?.id])

  const loadFavoriteCategories = async () => {
    if (!user?.id) return
    
    const { data, error } = await getUserFavoriteCategories(user.id)
    if (!error && data) {
      setFavoriteCategories(data.map(item => item.category))
    }
  }

  const handleInputChange = (field: string, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSocialLinkChange = (platform: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      social_links: { ...prev.social_links, [platform]: value }
    }))
  }

  const addInterest = (interest: string) => {
    if (interest && !formData.interests.includes(interest)) {
      setFormData(prev => ({
        ...prev,
        interests: [...prev.interests, interest]
      }))
      setNewCategory('')
    }
  }

  const removeInterest = (interest: string) => {
    setFormData(prev => ({
      ...prev,
      interests: prev.interests.filter(i => i !== interest)
    }))
  }

  const toggleFavoriteCategory = async (category: string) => {
    if (!user?.id) return

    if (favoriteCategories.includes(category)) {
      const { error } = await removeFavoriteCategory(user.id, category)
      if (!error) {
        setFavoriteCategories(prev => prev.filter(c => c !== category))
        toast.success(`Removed ${category} from favorites`)
      }
    } else {
      const { error } = await addFavoriteCategory(user.id, category)
      if (!error) {
        setFavoriteCategories(prev => [...prev, category])
        toast.success(`Added ${category} to favorites`)
      }
    }
  }

  const handleSave = async () => {
    if (!user?.id) return

    setLoading(true)
    try {
      // Clean up social links (remove empty values)
      const cleanSocialLinks = Object.fromEntries(
        Object.entries(formData.social_links).filter(([_, value]) => value.trim() !== '')
      )

      const { error } = await updateUserProfile(user.id, {
        ...formData,
        social_links: cleanSocialLinks,
        updated_at: new Date().toISOString()
      })

      if (error) {
        toast.error('Failed to update profile')
        console.error('Profile update error:', error)
      } else {
        toast.success('Profile updated successfully! 🎉')
        await refreshProfile()
        onOpenChange(false)
      }
    } catch (error) {
      toast.error('An unexpected error occurred')
      console.error('Profile update error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl gradient-text">
            <User className="h-6 w-6" />
            Edit Your Profile
          </DialogTitle>
          <DialogDescription>
            Make your profile shine! ✨ Update your information and let others know who you are.
          </DialogDescription>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
          {/* Left Column - Basic Info */}
          <div className="space-y-6">
            <Card className="glass-effect">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <User className="h-5 w-5 text-primary" />
                  Basic Information
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="full_name">Full Name</Label>
                    <Input
                      id="full_name"
                      value={formData.full_name}
                      onChange={(e) => handleInputChange('full_name', e.target.value)}
                      placeholder="Your awesome name 😎"
                    />
                  </div>

                  <div>
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      value={formData.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      placeholder="Tell us about yourself... What makes you unique? 🌟"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="university">University</Label>
                      <Input
                        id="university"
                        value={formData.university}
                        onChange={(e) => handleInputChange('university', e.target.value)}
                        placeholder="Your amazing university 🎓"
                      />
                    </div>

                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={formData.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        placeholder="Where are you based? 📍"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                        placeholder="Your contact number 📱"
                      />
                    </div>

                    <div>
                      <Label htmlFor="date_of_birth">Date of Birth</Label>
                      <Input
                        id="date_of_birth"
                        type="date"
                        value={formData.date_of_birth}
                        onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Interests & Social */}
          <div className="space-y-6">
            <Card className="glass-effect">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Heart className="h-5 w-5 text-accent" />
                  Interests & Hobbies
                </h3>
                
                <div className="space-y-4">
                  <div>
                    <Label>Add Interest</Label>
                    <div className="flex gap-2">
                      <Input
                        value={newCategory}
                        onChange={(e) => setNewCategory(e.target.value)}
                        placeholder="What are you passionate about? 🔥"
                        onKeyPress={(e) => e.key === 'Enter' && addInterest(newCategory)}
                      />
                      <Button 
                        onClick={() => addInterest(newCategory)}
                        size="icon"
                        variant="outline"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div>
                    <Label>Your Interests</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {formData.interests.map((interest) => (
                        <Badge 
                          key={interest} 
                          variant="secondary" 
                          className="cursor-pointer hover:bg-destructive/20"
                          onClick={() => removeInterest(interest)}
                        >
                          {interest}
                          <X className="h-3 w-3 ml-1" />
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="glass-effect">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                  <Heart className="h-5 w-5 text-accent" />
                  Favorite Event Categories
                </h3>
                
                <div className="grid grid-cols-2 gap-2">
                  {availableCategories.map((category) => (
                    <Badge
                      key={category}
                      variant={favoriteCategories.includes(category) ? "default" : "outline"}
                      className="cursor-pointer justify-center py-2 hover:scale-105 transition-transform"
                      onClick={() => toggleFavoriteCategory(category)}
                    >
                      {category}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Social Links Section */}
        <Card className="glass-effect mt-6">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Globe className="h-5 w-5 text-primary" />
              Social Links
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {socialPlatforms.map((platform) => {
                const Icon = platform.icon
                return (
                  <div key={platform.key}>
                    <Label htmlFor={platform.key} className="flex items-center gap-2">
                      <Icon className="h-4 w-4" />
                      {platform.label}
                    </Label>
                    <Input
                      id={platform.key}
                      value={formData.social_links[platform.key] || ''}
                      onChange={(e) => handleSocialLinkChange(platform.key, e.target.value)}
                      placeholder={platform.placeholder}
                    />
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={loading}
            className="bg-gradient-to-r from-primary to-accent hover:opacity-90"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
