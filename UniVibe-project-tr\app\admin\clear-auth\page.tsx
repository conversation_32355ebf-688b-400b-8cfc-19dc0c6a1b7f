"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { createSupabaseBrowserClient } from '@/lib/supabase'

export default function ClearAuthPage() {
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const supabase = createSupabaseBrowserClient()

  const clearAllAuth = async () => {
    setLoading(true)
    setError('')
    setMessage('Starting complete cleanup...')

    try {
      // Step 1: Clear all database tables
      console.log('Step 1: Clearing database tables...')
      setMessage('Clearing database tables...')

      const { error: userFavError } = await supabase.from('user_favorite_categories').delete().neq('user_id', '00000000-0000-0000-0000-000000000000')
      const { error: notifError } = await supabase.from('notifications').delete().neq('user_id', '00000000-0000-0000-0000-000000000000')
      const { error: rsvpError } = await supabase.from('rsvps').delete().neq('user_id', '00000000-0000-0000-0000-000000000000')
      const { error: eventError } = await supabase.from('events').delete().neq('organizer_id', '00000000-0000-0000-0000-000000000000')
      const { error: usersError } = await supabase.from('users').delete().neq('id', '00000000-0000-0000-0000-000000000000')

      if (usersError) {
        console.error('Error clearing users table:', usersError)
        setError(`Error clearing users table: ${usersError.message}`)
        return
      }

      // Step 2: Sign out current user from Supabase Auth
      console.log('Step 2: Signing out from Supabase Auth...')
      setMessage('Signing out from Supabase Auth...')
      await supabase.auth.signOut()

      // Step 3: Clear all browser storage
      console.log('Step 3: Clearing browser storage...')
      setMessage('Clearing browser storage...')
      if (typeof window !== 'undefined') {
        // Clear localStorage
        localStorage.clear()
        sessionStorage.clear()

        // Clear all cookies for this domain
        document.cookie.split(";").forEach((c) => {
          const eqPos = c.indexOf("=")
          const name = eqPos > -1 ? c.substr(0, eqPos).trim() : c.trim()
          // Clear for current path
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/"
          // Clear for domain
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=" + window.location.hostname
          // Clear for subdomain
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=." + window.location.hostname
        })

        // Clear IndexedDB (Supabase might store data here)
        if ('indexedDB' in window) {
          try {
            const databases = await indexedDB.databases()
            for (const db of databases) {
              if (db.name) {
                console.log('Deleting IndexedDB:', db.name)
                indexedDB.deleteDatabase(db.name)
              }
            }
          } catch (idbError) {
            console.log('IndexedDB cleanup error (non-critical):', idbError)
          }
        }
      }

      console.log('✅ Complete cleanup successful!')
      setMessage('✅ Complete cleanup successful! All auth data cleared. Redirecting to login...')

      // Redirect to login after a delay
      setTimeout(() => {
        window.location.href = '/auth/login'
      }, 3000)

    } catch (error) {
      console.error('Error during cleanup:', error)
      setError(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-red-600">Clear All Auth Data</CardTitle>
          <CardDescription>
            This will remove all users and authentication data from the database
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {message && (
            <Alert>
              <AlertDescription className="text-green-600">{message}</AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="font-medium text-red-800 mb-2">🚨 Complete Reset</h4>
              <p className="text-sm text-red-700">
                This action will completely reset everything:
              </p>
              <ul className="text-sm text-red-700 mt-2 list-disc list-inside space-y-1">
                <li>Delete ALL user profiles from the database</li>
                <li>Clear ALL events, RSVPs, and notifications</li>
                <li>Clear ALL Supabase Auth users</li>
                <li>Sign out the current user</li>
                <li>Clear ALL browser storage (cookies, localStorage, etc.)</li>
                <li>Clear IndexedDB databases</li>
                <li>Redirect to login page</li>
              </ul>
              <p className="text-sm text-red-700 mt-2 font-medium">
                ✅ After this, you'll have a completely clean slate!
              </p>
            </div>

            <Button
              onClick={clearAllAuth}
              disabled={loading}
              variant="destructive"
              className="w-full"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Clearing...
                </div>
              ) : (
                'Clear All Auth Data'
              )}
            </Button>

            <div className="text-center">
              <Button variant="outline" asChild>
                <a href="/auth/login">Cancel - Go to Login</a>
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
